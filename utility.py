import os
import pandas as pd
import logger_config
import math

logger = logger_config.setup_logger('main_logger', 'main.log')

# Determine sell option prices based on VIX
vix_price_map = {
    9: 14, 10: 15, 11: 16, 12:17, 13: 18, 14: 19, 15:20, 16: 20,
    17: 21, 18: 21, 19: 22, 20: 22, 21: 23
}

def round_vix(vix):
    """Round VIX value: 13.5 and above rounds up, below 13.5 rounds down."""
    return math.ceil(vix) if vix % 1 >= 0.5 else math.floor(vix)

def get_sell_option_price(vix):
    vix = round_vix(vix)
    logger.info(f"Rounded off vix close value : {vix}")
    #price = 35
    if vix <=8:
        price = 12
    elif vix >= 22:
        price = 24
    else:
        price = vix_price_map.get(vix) # Use vix_price_map for vix values between 9 and 21
    return float(price)

def get_calender_diff_value(price, vix):
    vix = round_vix(vix)
    if vix <= 13:
        diff = 10
    elif 14 <= vix <= 15:
        diff = 8
    elif 16 <= vix <= 18:
        diff = 5
    elif 19 <= vix <= 21:
        diff = 0
    elif vix >= 22:
        diff = -5
    return price + diff

def find_closest_option(folder_path, target_date, target_price, start_time):

    #print(f"folder_path: {folder_path}, target_date: {target_date}, target_price: {target_price}, start_time: {start_time}")
    """
    Finds the row in the folder whose 'Close' is closest to the target price for a specific date.
    Returns (symbol, row with Date, Time, Close).
    """
    closest_diff = float('inf')
    selected_row = None
    selected_file = None
    start_time = start_time.strftime("%H:%M")

    for file_name in os.listdir(folder_path):

        if not file_name.endswith('.parquet'):
            continue

        file_path = os.path.join(folder_path, file_name)

        try:
            df = pd.read_parquet(file_path)
        except Exception as e:
            continue

        if "YMD" not in df.columns or "Close" not in df.columns:
            continue  

        df["YMD"] = df["YMD"].astype(str)
        df["Time"] = df["Time"].astype(str)
    
        df = df[(df["YMD"] == target_date) & (df["Time"] == start_time)]


        if df.empty:
            continue
       
        df["Close"] = pd.to_numeric(df["Close"], errors='coerce')
        df.dropna(subset=["Close"], inplace=True)
        df["diff"] = (df["Close"] - target_price).abs()

        min_row = df.loc[df["diff"].idxmin()]

        if min_row["diff"] < closest_diff:
            closest_diff = min_row["diff"]
            selected_row = min_row
            selected_file = file_name        
            selected_symbol = os.path.splitext(selected_file)[0]
        
    return (selected_symbol, selected_row) if selected_row is not None else (None, None)
   