import os, json
from concurrent.futures import ThreadPoolExecutor
from utility import get_sell_option_price, get_calender_diff_value, find_closest_option
from VIX_Threshold import get_vix
import logger_config
import time

logger = logger_config.setup_logger('main_logger', 'main.log')

sides = {
    "CE_SELL": "SELL",
    "PE_SELL": "SELL",
    "CE_BUY": "BUY",
    "PE_BUY": "BUY"
}

def thursday_execute_BCS(mainkey, subkey, start_time, exit_time, folder_date_dt):
    start = time.time()
    
    target_date = os.path.basename(mainkey)
    logger.info(f"Target Date: {target_date}")
    
    vix_close_value = get_vix(target_date,start_time)
    if vix_close_value is None:
        logger.info(f"Skipping processing for {target_date} due to missing VIX data.")
        return None

    logger.info(f"get_vix time: {time.time() - start:.2f}s")

    start = time.time()

    sell_option_price = get_sell_option_price(vix_close_value)
    logger.info(f"sell_option_price: {sell_option_price}")
    calender_option_price = get_calender_diff_value(sell_option_price, vix_close_value)
    logger.info(f"calender_option_price: {calender_option_price}")
    logger.info(f"get_sell_option_price & calender_diff time: {time.time() - start:.2f}s")

    weekday_numbers = json.loads(os.getenv("WEEKDAY_NUMBERS"))
    resize = float(os.getenv(
        "RESIZE_WEDNESDAY" if folder_date_dt.weekday() == weekday_numbers["WEDNESDAY"]
        else "RESIZE_THURSDAY" if folder_date_dt.weekday() == weekday_numbers["THURSDAY"]
        else "RESIZE_NORMAL"
    ))
    logger.info(f"Resize: {resize}")
    sell_option_price *= resize
    logger.info(f"sell_option_price after resize: {sell_option_price}")
    calender_option_price *= resize
    logger.info(f"calender_option_price after resize: {calender_option_price}")
   
    results = {}

    def process_option(subkey, side, target_price):
        folder_path = os.path.join(mainkey, subkey)
        symbol, row = find_closest_option(folder_path, target_date, target_price, start_time)
        logger.info(f"Found option from {subkey}: {symbol}")
        if row is not None:
            return subkey, {
                "Date": row["YMD"],
                "Time": row["Time"],
                "symbol": symbol,
                "ltp": float(row["Close"]),
                "side": side
            }
            
        return subkey, None

    # 🔧 Dynamically set max_workers based on CPU
    cpu_count = os.cpu_count()
    max_workers = max(1, cpu_count - 1)
    logger.info(f"Using {max_workers} threads for processing.")
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = []
        for subkey, side in sides.items():
            logger.info(f"Processing {subkey}...")
            price = sell_option_price if side == "SELL" else calender_option_price
            futures.append(executor.submit(process_option, subkey, side, price))

        for f in futures:
            subkey, result = f.result()
            if result:
                results[subkey] = result
    
      # Check if any options are missing
    missing_options = [subkey for subkey in sides if subkey not in results]

    if missing_options:
        logger.warning(f"Skipping processing for {target_date} due to missing options: {', '.join(missing_options)}")
        return None

    return results, vix_close_value
    
