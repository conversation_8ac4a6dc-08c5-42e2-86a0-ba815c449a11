import os
import re
import time
from datetime import datetime, timedelta
from BT_Thursday_New_Recreate_Strategy import thursday_execute_BCS
from Monitor_Prices import monitor_prices
import logger_config
import random
from datetime import date
from Generate_Equity_Curve import generate_equity_curve
import csv
from ratio_hit_analysis import export_ratio_hit_analysis
import pandas as pd

logger = logger_config.setup_logger('main_logger', 'main.log')


def get_randomized_times(target_date):
    """Calculate randomized start and exit times based on configuration."""
   
    # Seed random with the provided trading date for consistency throughout the day
    random.seed(target_date.isoformat())
    
    # Get base times from environment
    start_base = datetime.strptime(os.getenv('START_BASE_TIME'), '%H:%M').time()
    exit_base = datetime.strptime(os.getenv('EXIT_BASE_TIME'), '%H:%M').time()
    
    # Get variation ranges
    start_variation = int(os.getenv('START_TIME_VARIATION', '10'))
    exit_variation = int(os.getenv('EXIT_TIME_VARIATION', '10'))
    
    # Calculate random offsets
    start_offset = random.randint(-start_variation, start_variation)
    exit_offset = random.randint(-exit_variation, exit_variation)
    
    # Apply offsets
    start_dt = datetime.combine(date.today(), start_base)
    exit_dt = datetime.combine(date.today(), exit_base)
    
    start_dt = start_dt + timedelta(minutes=start_offset)
    exit_dt = exit_dt + timedelta(minutes=exit_offset)
    
    return start_dt.time(), exit_dt.time()


# Function to load Parquet files into memory
def load_parquet_data(folders):
    data = {}
    for folder in folders:
        data[folder] = {}
        for subfolder in ['CE_BUY', 'CE_SELL', 'PE_BUY', 'PE_SELL']:
            subfolder_path = os.path.join(folder, subfolder)
            parquet_files = [f for f in os.listdir(subfolder_path) if f.endswith('.parquet')]
            data[folder][subfolder] = [pd.read_parquet(os.path.join(subfolder_path, f)) for f in parquet_files]
    return data

def run_for_one_day(mainkey: str, subkey: dict) -> None:
    """
    Run the existing back‑test pipeline for a single YYYYMMDD folder.
    This is your original code, moved unchanged into its own function.
    """
    start_time_process = time.time()
    logger.info("Starting the main process...")

    # --- Identify the date from the folder name --- #
    folder_date = os.path.basename(mainkey)
    folder_date_dt = datetime.strptime(folder_date, "%Y%m%d")
    logger.info(f"Folder Date: {folder_date_dt.strftime('%Y-%m-%d')}")

    # --- Subfolders and their sides (unchanged) --- #
    sides = {
        "CE_SELL": "SELL",
        "PE_SELL": "SELL",
        "CE_BUY":  "BUY",
        "PE_BUY":  "BUY"
    }

    # --- Get randomized start/exit times --- #
    start_execution_time = time.time()
    #start_time, exit_time = get_randomized_times(folder_date_dt)

    # --- Use hardcoded start/exit times --- #
    start_time = datetime.strptime("09:45:00", "%H:%M:%S").time()
    exit_time = datetime.strptime("15:10:00", "%H:%M:%S").time()

    print(start_time)
    print(type(start_time))
    print(exit_time)
    print(type(exit_time))
    
    logger.info(f"Backtesting trading window: Start at {start_time}, Exit at {exit_time}")
    logger.info(f"get_randomized_times() execution time: {time.time()-start_execution_time:.6f}s")

    # --- Strategy + monitoring (unchanged) --- #
    start_execution_time = time.time()
    BCS_positions, vix_close_value = thursday_execute_BCS(mainkey, subkey, start_time, exit_time, folder_date_dt)
    
    if BCS_positions is None:
        logger.info(f"Skipping folder {mainkey} due to missing VIX data or other issues.")
        return None, None
    logger.info(f"BCS_positions: {BCS_positions}")
    logger.info(f"thursday_execute_BCS() execution time: {time.time()-start_execution_time:.6f}s")

    start_execution_time = time.time()
    total_pnl, exit_reason  =monitor_prices(BCS_positions, mainkey, start_time, exit_time, folder_date)
    logger.info(f"monitor_prices() execution time: {time.time()-start_execution_time:.6f}s")

    # --- Finish up --- #
    processing_time = time.time() - start_time_process
    logger.info(f"Processing time: {str(timedelta(seconds=int(processing_time)))}")
    logger.info("End of day ----------------------------------------------\n")
    
    return total_pnl, exit_reason, vix_close_value


# ------------------ Folder Filtering ------------------ #
def list_date_folders(root_path, from_date, to_date):
    date_folder_pattern = re.compile(r"^\d{8}$")
    all_entries = [
        os.path.join(root_path, name)
        for name in os.listdir(root_path)
        if date_folder_pattern.match(name) and os.path.isdir(os.path.join(root_path, name))
    ]
    all_entries.sort()
    filtered_entries = [
        f for f in all_entries
        if from_date <= datetime.strptime(os.path.basename(f), "%Y%m%d") <= to_date
    ]

    valid_folders = []
    skipped_dates = []

    for folder in filtered_entries:
        date_str = os.path.basename(folder)
        subfolders = ['CE_BUY', 'CE_SELL', 'PE_BUY', 'PE_SELL']
        valid = all(
            os.path.exists(os.path.join(folder, sub)) and
            any(f.endswith('.parquet') for f in os.listdir(os.path.join(folder, sub)))
            for sub in subfolders
        )
        if valid:
            valid_folders.append(folder)
        else:
            skipped_dates.append(date_str)

    with open('skipped_folders.csv', mode='w', newline='') as file:
        writer = csv.writer(file)
        writer.writerow(['Skipped_Folder_Date'])
        for date in skipped_dates:
            writer.writerow([date])

    print(f"Found {len(valid_folders)} valid folders between {from_date.date()} and {to_date.date()}")
    print(f"Skipped {len(skipped_dates)} folders due to missing or empty subfolders.")
    
    return valid_folders

# ------------- 2. NEW driver that loops through all date folders ------------- #
def main():
    start_time_main = time.time()
    
    root_path = r"C:\Users\<USER>\YatinBhatia\Thursday_BackTesting2\Parquet_Files\Thursday_output_folder"
    
    all_folders = list_date_folders(root_path, datetime(2021, 1, 1), datetime(2021, 1, 10))

    # Load all Parquet data into memory
    data = load_parquet_data(all_folders)

    
    #Start printing data dict for debugging purpose
    
    # Print the top-level keys of the data dictionary
    print("Top-level keys in data dictionary:", data.keys())

    # Iterate over the top-level keys and print the sub-keys and a sample of the data
    for folder, subfolders in data.items():
        print(f"\nFolder: {folder}")
        for subfolder, dataframes in subfolders.items():
            print(f"  Subfolder: {subfolder}")
            if dataframes:
                # Print the first few rows of the first dataframe as a sample
                print("    Sample data:")
                print(dataframes[0].head())
                print(dataframes[1].head())
            else:
                print("    No dataframes found.")

    #End printing data dict for debugging purpose

   

    if not data:
        logger.warning("No dataframes loaded. Please check the directory and file extensions.")
        return
    
    exit_reason_counts = {
    "STOP_LOSS": 0,
    "TARGET_PROFIT": 0,
    "RATIO_HIT": 0,
    "TIME_EXIT": 0
    }
    
    # Retrieve start and end dates from environment variables
    start_date_str = os.getenv("START_DATE")
    logger.info(f"START_DATE: {start_date_str}")
    end_date_str = os.getenv("END_DATE")
    logger.info(f"END_DATE: {end_date_str}")

    try:
        from_date = datetime.strptime(start_date_str, "%Y%m%d")
        to_date = datetime.strptime(end_date_str, "%Y%m%d")
    except ValueError as e:
        logger.error(f"Invalid date format in environment variables: {e}")
        return

    equity_log = []  # Store date-wise total_pnl here

    # ---- Run the original logic for each day ---- #
    for mainkey, subkey in data.items():
        folder_date = None  # Initialize folder_date before the try block
        try:
            # Pass the entire subfolder data to run_for_one_day
            pnl, exit_reason, vix_close_value = run_for_one_day(mainkey, subkey)
            folder_date = os.path.basename(mainkey)
            equity_log.append({'date': folder_date, 'pnl': pnl, 'exit_reason': exit_reason, 'vix_close_value': vix_close_value})
            
            if exit_reason is not None:
                exit_reason_counts[exit_reason] += 1
            else:
                logger.warning(f"No exit reason for folder {folder_date}. Skipping count update.")

        except Exception as e:
            logger.exception(f"Error while processing folder {folder_date}: {e}")

    # Generate ratio hit analysis
    #export_ratio_hit_analysis(equity_log, output_dir=".")

     # Generate equity curve
    #generate_equity_curve(equity_log, output_dir=".")

    processing_time = time.time() - start_time_main
    logger.info(f"Total execution time: {str(timedelta(seconds=int(processing_time)))}")

    logger.info("----- Exit Reason Summary -----")
    for reason, count in exit_reason_counts.items():
        logger.info(f"{reason}: {count}")


if __name__ == "__main__":
    main()
